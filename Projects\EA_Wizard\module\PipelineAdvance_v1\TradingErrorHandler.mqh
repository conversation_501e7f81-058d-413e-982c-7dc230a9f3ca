#property strict

//+------------------------------------------------------------------+
//| TradingErrorHandler class definition                             |
//| 專為 PipelineAdvance_v1 模組設計的錯誤處理器                    |
//+------------------------------------------------------------------+

// 防止重複包含
#ifndef _TRADING_ERROR_HANDLER_MQH_
#define _TRADING_ERROR_HANDLER_MQH_

// 引入必要的模組
#include "TradingEvent.mqh"
#include "../MQL4Logger/FileLog.mqh"

// 前向聲明
class PipelineResult;

//+------------------------------------------------------------------+
//| 錯誤記錄項目結構                                                 |
//+------------------------------------------------------------------+
struct TradingErrorRecord
{
    string m_message;                    // 錯誤消息
    string m_source;                     // 錯誤來源
    ENUM_ERROR_LEVEL m_errorLevel;       // 錯誤級別
    datetime m_timestamp;                // 時間戳

    // 構造函數
    TradingErrorRecord(string message = "", string source = "", ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_ERROR)
        : m_message(message), m_source(source), m_errorLevel(errorLevel), m_timestamp(TimeCurrent()) {}
};

//+------------------------------------------------------------------+
//| TradingErrorHandler 類別 - 單例模式                             |
//+------------------------------------------------------------------+
class TradingErrorHandler
{
private:
    string m_last_error;                 // 最後一個錯誤消息
    TradingErrorRecord m_error_log[];    // 錯誤記錄陣列
    int m_error_count;                   // 錯誤計數
    int m_max_errors;                    // 最大錯誤數量
    CFileLog* m_logger;                  // 日誌記錄器
    bool m_logger_owned;                 // 是否擁有日誌記錄器
    static TradingErrorHandler* g_instance; // 單例實例

    // 私有構造函數 - 單例模式
    TradingErrorHandler()
    {
        m_last_error = "";
        m_error_count = 0;
        m_max_errors = 50;  // 默認最大錯誤數量
        m_logger = NULL;
        m_logger_owned = false;
        ArrayResize(m_error_log, m_max_errors);
    }

    // 私有析構函數
    ~TradingErrorHandler()
    {
        if(m_logger_owned && m_logger != NULL)
        {
            delete m_logger;
            m_logger = NULL;
        }
        ArrayFree(m_error_log);
    }

public:
    // 獲取單例實例
    static TradingErrorHandler* GetInstance()
    {
        if(g_instance == NULL)
            g_instance = new TradingErrorHandler();
        return g_instance;
    }

    // 釋放單例實例
    static void ReleaseInstance()
    {
        if(g_instance != NULL)
        {
            delete g_instance;
            g_instance = NULL;
        }
    }

    // 設置最大錯誤數量
    void SetMaxErrors(int maxErr)
    {
        if(maxErr > 0)
        {
            m_max_errors = maxErr;
            ArrayResize(m_error_log, m_max_errors);
            if(m_error_count > m_max_errors)
                m_error_count = m_max_errors;
        }
    }

    // 設置日誌記錄器
    void SetLogger(CFileLog* logger, bool owned = false)
    {
        if(m_logger_owned && m_logger != NULL)
        {
            delete m_logger;
        }
        m_logger = logger;
        m_logger_owned = owned;
    }

    // 初始化默認日誌記錄器
    void InitializeDefaultLogger(string filename = "TradingErrorHandler.log")
    {
        if(m_logger == NULL)
        {
            m_logger = new CFileLog(filename, ERROR, true, true);
            m_logger_owned = true;
        }
    }

    // 處理錯誤 - 基本版本
    void HandleError(string error, string source = "", ENUM_ERROR_LEVEL errorLevel = ERROR_LEVEL_ERROR)
    {
        m_last_error = error;

        // 創建錯誤記錄
        TradingErrorRecord record(error, source, errorLevel);

        // 添加到錯誤日誌
        AddErrorRecord(record);

        // 記錄到日誌
        LogError(record);
    }

    // 處理 PipelineResult 錯誤 - 專門方法
    void HandlePipelineResult(PipelineResult* result);  // 實現將在包含 PipelineResult 定義後提供

    // 批量處理 PipelineResult 陣列
    void HandlePipelineResults(PipelineResult* results[], int count);  // 實現將在包含 PipelineResult 定義後提供

    // 記錄錯誤到日誌
    void LogError(const TradingErrorRecord& record)
    {
        string levelStr = ErrorLevelToString(record.m_errorLevel);
        string logMessage = StringFormat("[%s] %s: %s",
                                       levelStr,
                                       record.m_source,
                                       record.m_message);

        if(m_logger != NULL)
        {
            // 根據錯誤級別選擇適當的日誌方法
            switch(record.m_errorLevel)
            {
                case ERROR_LEVEL_INFO:
                    m_logger.Info(logMessage);
                    break;
                case ERROR_LEVEL_WARNING:
                    m_logger.Warning(logMessage);
                    break;
                case ERROR_LEVEL_ERROR:
                    m_logger.Error(logMessage);
                    break;
                case ERROR_LEVEL_CRITICAL:
                    m_logger.Critical(logMessage);
                    break;
                default:
                    m_logger.Error(logMessage);
                    break;
            }
        }
        else
        {
            // 如果沒有日誌記錄器，使用 Print
            Print("TradingErrorHandler: ", logMessage);
        }
    }

    // 清除所有錯誤
    void ClearErrors()
    {
        ArrayFree(m_error_log);
        ArrayResize(m_error_log, m_max_errors);
        m_error_count = 0;
        m_last_error = "";
    }

    // 獲取最後一個錯誤
    string GetLastError() const
    {
        return m_last_error;
    }

    // 獲取錯誤計數
    int GetErrorCount() const
    {
        return m_error_count;
    }

    // 獲取指定級別的錯誤計數
    int GetErrorCountByLevel(ENUM_ERROR_LEVEL level) const
    {
        int count = 0;
        for(int i = 0; i < m_error_count; i++)
        {
            if(m_error_log[i].m_errorLevel == level)
                count++;
        }
        return count;
    }

    // 檢查是否有嚴重錯誤
    bool HasCriticalErrors() const
    {
        return GetErrorCountByLevel(ERROR_LEVEL_CRITICAL) > 0;
    }

    // 獲取錯誤記錄
    bool GetErrorRecord(int index, TradingErrorRecord& record) const
    {
        if(index >= 0 && index < m_error_count)
        {
            record = m_error_log[index];
            return true;
        }
        return false;
    }

    // 獲取所有錯誤的摘要
    string GetErrorSummary() const
    {
        string summary = StringFormat("錯誤總數: %d\n", m_error_count);
        summary += StringFormat("信息: %d, 警告: %d, 錯誤: %d, 嚴重: %d",
                               GetErrorCountByLevel(ERROR_LEVEL_INFO),
                               GetErrorCountByLevel(ERROR_LEVEL_WARNING),
                               GetErrorCountByLevel(ERROR_LEVEL_ERROR),
                               GetErrorCountByLevel(ERROR_LEVEL_CRITICAL));
        return summary;
    }

private:
    // 添加錯誤記錄到陣列
    void AddErrorRecord(const TradingErrorRecord& record)
    {
        if(m_error_count < m_max_errors)
        {
            m_error_log[m_error_count] = record;
            m_error_count++;
        }
        else
        {
            // 移動陣列元素，為新錯誤騰出空間
            for(int i = 1; i < m_error_count; i++)
            {
                m_error_log[i-1] = m_error_log[i];
            }
            m_error_log[m_max_errors - 1] = record;
        }
    }

    // 將錯誤級別轉換為字符串
    string ErrorLevelToString(ENUM_ERROR_LEVEL level) const
    {
        switch(level)
        {
            case ERROR_LEVEL_INFO:     return "INFO";
            case ERROR_LEVEL_WARNING:  return "WARNING";
            case ERROR_LEVEL_ERROR:    return "ERROR";
            case ERROR_LEVEL_CRITICAL: return "CRITICAL";
            default:                   return "UNKNOWN";
        }
    }
};

// 初始化靜態實例指針
TradingErrorHandler* TradingErrorHandler::g_instance = NULL;

//+------------------------------------------------------------------+
//| PipelineResult 相關方法的實現                                    |
//| 注意：這些方法需要在包含 PipelineResult 定義後才能使用           |
//+------------------------------------------------------------------+

// 包含 PipelineResult 定義的宏
#ifdef PIPELINE_RESULT_DEFINED

// 處理 PipelineResult 錯誤的實現
void TradingErrorHandler::HandlePipelineResult(PipelineResult* result)
{
    if(result == NULL) return;

    if(!result.IsSuccess())
    {
        HandleError(result.GetMessage(), result.GetSource(), result.GetErrorLevel());
    }
}

// 批量處理 PipelineResult 陣列的實現
void TradingErrorHandler::HandlePipelineResults(PipelineResult* results[], int count)
{
    for(int i = 0; i < count; i++)
    {
        if(results[i] != NULL)
        {
            HandlePipelineResult(results[i]);
        }
    }
}

#endif // PIPELINE_RESULT_DEFINED

// 結束防止重複包含
#endif // _TRADING_ERROR_HANDLER_MQH_
